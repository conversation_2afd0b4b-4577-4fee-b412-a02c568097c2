import { IPackageUpcomingStatus } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdatePackageUpcomingStatus(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageUpcomingStatus>, Error, IPackageUpcomingStatus>({
    mutationFn: (data: IPackageUpcomingStatus) =>
      fetch(`/api/package-upcoming/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-upcoming-status'] });
      queryClient.invalidateQueries({ queryKey: ['packages-upcoming-status', id] });
    },
    onError: () => {
      toast.error('Error updating package Upcoming Status');
    },
  });
}
