import { IPackageCostDate } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreatePackageCostDate() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageCostDate>, Error, any>({
    mutationFn: (data: any) =>
      fetch(`/api/package-cost-date`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-cost-date'] });
      toast.success('Package cost date created successfully');
    },
    onError: () => {
      toast.error('Error creating package');
    },
  });
}
