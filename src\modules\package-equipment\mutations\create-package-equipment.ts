import { IPackageEquipment } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreatePackageEquipment() {
  const queryClient = useQueryClient();
  return useMutation<
    IApiResponse<IPackageEquipment>,
    Error,
    Omit<IPackageEquipment, 'id'>
  >({
    mutationFn: (data: Omit<IPackageEquipment, 'id'>) =>
      fetch(`/api/package-equipment`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-equipment'] });
      toast.success('Package equipment created successfully');
    },
    onError: () => {
      toast.error('Error creating package equipment');
    },
  });
}
