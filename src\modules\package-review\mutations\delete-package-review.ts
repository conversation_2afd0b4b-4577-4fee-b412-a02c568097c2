import { IPackageReview } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeletePackageReview(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageReview>, Error, string>({
    mutationFn: (reviewId: string) =>
      fetch(`/api/package-review/${reviewId}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-review'] });
      toast.success('Package deleted successfully');
    },
    onError: () => {
      toast.error('Error deleting package');
    },
  });
}
