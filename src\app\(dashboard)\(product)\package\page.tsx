'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useDeletePackage } from '@/modules/package/mutations/delete-package';
import { IPackage } from '@/types/package_';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { useGetPackages } from '@/modules/package/queries/get-packages';

export default function PackageListPage() {
  const [page, setPage] = useState(1);
  const limit = 10;
  const router = useRouter();

  const { data, isLoading, isError, error } = useGetPackages({
    page,
    limit,
    sort: 'createdAt:desc',
  });

  const deleteMutation = useDeletePackage('');

  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this package?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleEdit = (id: string) => {
    router.push(`/package/edit/${id}`);
  };

  if (isLoading) return <div>Loading packages...</div>;
  if (isError) return <div>Error loading packages: {error?.message}</div>;

  const packages = data?.data || [];
  const totalPages = data?.meta?.lastPage || 1;

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Packages List</h1>
        <Link href="/package/create">
          <Button className="bg-brand hover:bg-brand/80">Add Package</Button>
        </Link>
      </div>

      <table className="min-w-full border border-gray-200 table-auto">
        <thead className="bg-gray-100">
          <tr>
            <th className="border border-gray-300 p-2">SN</th>
            <th className="border border-gray-300 p-2">Name</th>
            <th className='border border-gray-300 p-2'>Slug</th>
            <th className="border border-gray-300 p-2">Activity</th>
            <th className="border border-gray-300 p-2">Region</th>
            <th className="border border-gray-300 p-2">Duration</th>
            <th className="border border-gray-300 p-2">Price</th>
            <th className="border border-gray-300 p-2">Actions</th>
          </tr>
        </thead>

        <tbody>
          {packages.map((pkg: IPackage, idx) => (
            <tr key={pkg.id} className="hover:bg-gray-50">
              <td className="border border-gray-300 p-2">{(page - 1) * limit + idx + 1}</td>
              <td className="border border-gray-300 p-2">{pkg.name}</td>
              <td className="border border-gray-300 p-2">{pkg.slug}</td>
              <td className="border border-gray-300 p-2">
                {pkg.activity?.name || 'N/A'}
              </td>
              <td className="border border-gray-300 p-2">
                {pkg.region?.name || 'N/A'}
              </td>
              <td className="border border-gray-300 p-2">{pkg.duration}</td>
              <td className="border border-gray-300 p-2">${pkg.price}</td>
              <td className="border border-gray-300 p-2 space-x-2 text-center">
                <Button
                  onClick={() => handleEdit(pkg.id)}
                  variant="outline"
                  size="sm"
                  className="px-3 py-1"
                >
                  Edit
                </Button>
                <Button
                  onClick={() => handleDelete(pkg.id)}
                  variant="destructive"
                  size="sm"
                  className="px-3 py-1"
                >
                  Delete
                </Button>
              </td>
            </tr>
          ))}
          {packages.length === 0 && (
            <tr>
              <td
                colSpan={6}
                className="border border-gray-300 p-4 text-center"
              >
                No packages found.
              </td>
            </tr>
          )}
        </tbody>
      </table>

      <div className="flex justify-between items-center mt-6">
        <Button
          onClick={() => setPage((p) => Math.max(1, p - 1))}
          disabled={page === 1}
          className="px-4 py-2"
        >
          Previous
        </Button>
        <span>
          Page {page} of {totalPages}
        </span>
        <Button
          onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
          disabled={page === totalPages}
          className="px-4 py-2"
        >
          Next
        </Button>
      </div>
    </div>
  );
}
