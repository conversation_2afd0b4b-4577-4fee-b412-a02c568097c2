import { IHomeSEO } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateHomeSEO() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IHomeSEO>, Error, IHomeSEO>({
    mutationFn: (data: IHomeSEO) =>
      fetch(`/api/home/<USER>/update`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['home-seo'] });
      toast.success('Home SEO Updated Successfully');
    },
    onError: () => {
      toast.error('Error Updating Home SEO');
    },
  });
}
