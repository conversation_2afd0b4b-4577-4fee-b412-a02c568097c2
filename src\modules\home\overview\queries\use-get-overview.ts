import { IExperience, IOverview } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetOverview() {
  return useQuery<IApiResponse<IOverview>, Error>({
    queryKey: ['overview'],
    queryFn: () =>
      fetch(`/api/home-overview`, {
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => res.json()),
  });
}
