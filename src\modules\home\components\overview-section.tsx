"use client";

import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import Link from "next/link";
import { Pen, Trash } from "lucide-react";
import { Button } from "@/components/ui/button";
import { UseGetOverview } from "../overview/queries/use-get-overview";
import { UseUpdateOverview } from "../overview/mutations/use-update-overview";
import Image from "next/image";

const OverviewSection: React.FC = () => {
  const { data, isLoading, error } = UseGetOverview();
  const updateOverview = UseUpdateOverview();
  const [overview, setOverview] = useState(data?.data || null);

  useEffect(() => {
    if (data?.data) {
      setOverview(data.data);
    }
  }, [data]);

  const handleDeleteFeature = (featureId: string) => {
    if (!overview) return;
    const confirmDelete = window.confirm("Are you sure you want to delete this feature?");
    if (!confirmDelete) return;

    const updatedFeatures = overview.points.filter(feature => feature.id !== featureId);
    const updatedOverview = { ...overview, points: updatedFeatures };

    updateOverview.mutate(updatedOverview, {
      onSuccess: () => {
        toast.success("Feature deleted successfully.");
        setOverview(updatedOverview);
      },
      onError: (err) => {
        toast.error(`Delete failed: ${err.message}`);
      }
    });
  };

  if (isLoading) return <p>Loading overview...</p>;
  if (error) return <p>Error loading overview: {error.message}</p>;

  if (!overview) {
    return (
      <div className="p-6 max-w-full mx-auto">
        <p className="mb-4 text-lg font-medium">No experience data found.</p>
        <button
          className="bg-black text-white px-4 py-2 rounded hover:bg-gray-900 transition"
          onClick={() => window.location.href = "/home/<USER>/create"}
        >
          Create Overview
        </button>
      </div>
    );
  }

  return (
    <div>
      <section className="p-6">
        <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h1 className="text-3xl font-bold">{overview.heading}</h1>
            <Link href={`/home/<USER>/edit`}>
              <Button>
                Edit Overview
              </Button>
            </Link>
          </div>
          {overview.description && (
            <p className="mb-6">{overview.description}</p>
          )}
          <div className="bg-white rounded-lg shadow">
            <table className="min-w-full text-left border-separate border-spacing-0">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border px-4 py-2">SN</th>
                  <th className="border px-4 py-2">Feature Title</th>
                  <th className="border px-4 py-2">Feature Icon</th>
                  <th className="border px-4 py-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {overview.points.map((feature, idx) => (
                  <tr key={idx} className="even:bg-gray-50">
                    <td className="border px-4 py-2">{idx + 1}</td>
                    <td className="border px-4 py-2">{feature.title}</td>
                    <td className="border px-4 py-2">
                      {feature.icon ? (
                        <Image
                          src={feature.icon}
                          alt={feature.title}
                          width={100}
                          height={60}
                          className="w-16 h-10 object-cover rounded"
                        />
                      ) : (
                        <span className="text-gray-400">No image</span>
                      )}
                    </td>
                    <td className="py-2 px-4 flex gap-2">
                      <div className="flex space-x-2">
                        <Link
                          href={`/home/<USER>/edit`}
                          className="inline-flex items-center px-3 py-1 border border-green-500 text-green-500 rounded hover:bg-green-50 transition-colors"
                        >
                          <Pen className="w-4 h-4" />
                        </Link>
                        <button
                          onClick={() => handleDeleteFeature(feature.id)}
                          className="inline-flex items-center px-3 py-1 border border-red-500 text-red-500 rounded hover:bg-red-50 transition-colors"
                          type="button"
                        >
                          <Trash className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
  );
};

export default OverviewSection;
