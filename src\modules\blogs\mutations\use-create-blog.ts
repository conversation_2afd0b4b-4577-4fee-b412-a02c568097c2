import { IBlog } from '@/types/blogs';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreateBlogs() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IBlog>, Error, any>({
    mutationFn: (data: any) =>
      fetch(`/api/blogs`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      toast.success('Blogs created successfully');
    },
    onError: () => {
      toast.error('Error creating Blogs');
    },
  });
}
