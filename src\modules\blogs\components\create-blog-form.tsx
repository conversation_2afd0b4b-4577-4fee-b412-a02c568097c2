'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import dynamic from 'next/dynamic';
import { IBlog } from '@/types/blogs';
import ImageUploadSingleWithMutation from '@/modules/images/components/upload-single-image';

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), {
    ssr: false,
});

interface BlogFormProps {
    blog?: IBlog;
    onSubmit: (data: any) => void;
    loading?: boolean;
}

export function BlogForm({ blog, onSubmit, loading }: BlogFormProps) {
    const router = useRouter();

    const [formData, setFormData] = useState({
        title: blog?.title || '',
        image: blog?.image || null,
        imageAltTag: blog?.imageAltTag || '',
        content: blog?.content || '',
        slug: blog?.slug || '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <Card className="w-full mx-auto">
            <CardHeader>
                <CardTitle>{blog ? 'Edit Blog' : 'Create New Blog'}</CardTitle>
                <CardDescription>
                    {blog
                        ? 'Update the blog information below.'
                        : 'Fill in the details to create a new blog post.'}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="space-y-2">
                        <Label htmlFor="title">Title</Label>
                        <Input
                            id="title"
                            value={formData.title}
                            onChange={(e) =>
                                setFormData((prev) => ({ ...prev, title: e.target.value }))
                            }
                            placeholder="Enter blog title"
                            required
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="slug">Slug</Label>
                        <Input
                            id="slug"
                            value={formData.slug}
                            onChange={(e) =>
                                setFormData((prev) => ({ ...prev, slug: e.target.value }))
                            }
                            placeholder="blog-slug"
                            required
                        />
                    </div>

                    <ImageUploadSingleWithMutation
                        label="Banner Image"
                        accept="image/*"
                        initialUrl={formData.image}
                        onUploaded={(url) => setFormData((prev) => ({ ...prev, image: url }))}
                    />

                    <div className="space-y-2">
                        <Label htmlFor="imageAltTag">Image Alt Tag</Label>
                        <Input
                            id="imageAltTag"
                            value={formData.imageAltTag}
                            onChange={(e) =>
                                setFormData((prev) => ({ ...prev, imageAltTag: e.target.value }))
                            }
                            placeholder="Enter Image Alt Tag"
                            required
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="description">Short Description</Label>
                        <Textarea
                            id="description"
                            value={formData.description}
                            onChange={(e) =>
                                setFormData((prev) => ({ ...prev, description: e.target.value }))
                            }
                            placeholder="Enter a brief description of your blog post..."
                            required
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="content">Content</Label>
                        <RichTextEditor
                            value={formData.content}
                            onChange={(value) =>
                                setFormData((prev) => ({ ...prev, content: value }))
                            }
                        />
                    </div>

                    <div className="flex gap-4 pt-4">
                        <Button type="submit" disabled={loading}>
                            {loading ? 'Saving...' : blog ? 'Update Blog' : 'Create Blog'}
                        </Button>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => router.back()}
                        >
                            Cancel
                        </Button>
                    </div>
                </form>
            </CardContent>
        </Card>
    );
}
