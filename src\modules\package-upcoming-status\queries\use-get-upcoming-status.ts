import { useQuery } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import {  IPackageUpcomingStatus } from '@/types/package_';

export function useGetPackageUpcomingStatusById(id: string) {
  return useQuery<IApiResponse<IPackageUpcomingStatus>, Error>({
    queryKey: ['package-upcoming-status', id],
    queryFn: () =>
      fetch(`/api/package-upcoming/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then(res => res.json()),
    enabled: !!id,
  });
}
