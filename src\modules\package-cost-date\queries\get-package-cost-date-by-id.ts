import { IPackageCostDate } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageCostDateById(id: string) {
  return useQuery<IApiResponse<IPackageCostDate>, Error>({
    queryKey: ['packages-cost-date', id],
    queryFn: () =>
      fetch(`/api/package-cost-date/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
