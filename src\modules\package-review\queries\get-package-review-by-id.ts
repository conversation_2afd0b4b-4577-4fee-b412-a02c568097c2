import { IPackageReview } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageReviewById(id: string) {
  return useQuery<IApiResponse<IPackageReview>, Error>({
    queryKey: ['packages-review', id],
    queryFn: () =>
      fetch(`/api/package-review/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
