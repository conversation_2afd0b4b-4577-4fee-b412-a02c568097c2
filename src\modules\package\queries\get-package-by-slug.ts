import { IPackage } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageBySlug(slug: string) {
  return useQuery<IApiResponse<IPackage>, Error>({
    queryKey: ['package', 'slug', slug],
    queryFn: () =>
      fetch(`/api/package/slug/${slug}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!slug,
  });
}
