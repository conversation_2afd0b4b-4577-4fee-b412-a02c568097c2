'use client';

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { IHomeSEO } from "@/types/home";
import { useGetHomeSEO } from "@/modules/home/<USER>/queries/get-home-seo";
import { useUpdateHomeSEO } from "@/modules/home/<USER>/mutations/update-home-seo";

const initialSEO: IHomeSEO = {
    id: "",
    metaTitle: "",
    metaDescription: "",
    metaKeywords: [],
    canonicalUrl: "",
    ogTitle: "",
    ogDescription: "",
    ogImage: "",
    ogType: "",
    ogUrl: "",
    ogSiteName: "",
    twitterTitle: "",
    twitterDescription: "",
    twitterImage: "",
    twitterCard: "",
    robots: "",
    language: "",
    structuredData: "",
    sitemapPriority: "",
    changefreq: "",
    customMetaTags: "",
};

export default function EditHomeSEOPage() {
    const router = useRouter();

    const { data, isLoading, error } = useGetHomeSEO();
    const updateMutation = useUpdateHomeSEO();

    const [seo, setSEO] = useState<IHomeSEO>(initialSEO);
    const [keywordsInput, setKeywordsInput] = useState(seo.metaKeywords?.join(", ") || "");


    useEffect(() => {
        if (data?.data && data.data.id) {
            setSEO({ ...initialSEO, ...data.data, metaKeywords: data.data.metaKeywords || [] });
            setKeywordsInput(data.data.metaKeywords?.join(", ") || "");
        }
    }, [data]);


    const handleChange = (field: keyof IHomeSEO, value: string) => {
        setSEO((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleKeywordsChange = (value: string) => {
        setSEO((prev) => ({
            ...prev,
            metaKeywords: value.split(",").map((s) => s.trim()).filter(Boolean),
        }));
    };

    const handleSave = () => {
        if (!seo.id) {
            alert("SEO ID missing, cannot save.");
            return;
        }

        const keywordsArray = keywordsInput
            .split(",")
            .map((s) => s.trim())
            .filter(Boolean);

        updateMutation.mutate(
            { ...seo, metaKeywords: keywordsArray },
            {
                onSuccess: () => {
                    router.push("/home");
                },
                onError: (error) => {
                    alert(`Failed to save SEO: ${error.message}`);
                },
            }
        );
    };


    if (isLoading) return <div>Loading SEO data...</div>;
    if (error) return <div>Error loading SEO data.</div>;

    return (
        <div className="container mx-auto p-6 ">
            <Card>
                <CardHeader>
                    <CardTitle>Edit Home SEO</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <Label className="mb-2">Meta Title</Label>
                        <Input
                            value={seo.metaTitle || ""}
                            onChange={(e) => handleChange("metaTitle", e.target.value)}
                            placeholder="Meta Title"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label className="mb-2">Meta Description</Label>
                        <Input
                            value={seo.metaDescription || ""}
                            onChange={(e) => handleChange("metaDescription", e.target.value)}
                            placeholder="Meta Description"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label className="mb-2">Meta Keywords (comma separated)</Label>
                        <Input
                            value={keywordsInput}
                            onChange={(e) => setKeywordsInput(e.target.value)}
                            placeholder="keyword1, keyword2"
                        />

                    </div>

                    <div>
                        <Label className="mb-2">Canonical URL</Label>
                        <Input
                            value={seo.canonicalUrl || ""}
                            onChange={(e) => handleChange("canonicalUrl", e.target.value)}
                            placeholder="https://example.com"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label className="mb-2">OG Title</Label>
                        <Input
                            value={seo.ogTitle || ""}
                            onChange={(e) => handleChange("ogTitle", e.target.value)}
                            placeholder="OG Title"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label className="mb-2">OG Description</Label>
                        <Input
                            value={seo.ogDescription || ""}
                            onChange={(e) => handleChange("ogDescription", e.target.value)}
                            placeholder="OG Description"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label className="mb-2">OG Image URL</Label>
                        <Input
                            value={seo.ogImage || ""}
                            onChange={(e) => handleChange("ogImage", e.target.value)}
                            placeholder="https://example.com/image.jpg"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>OG Type</Label>
                        <Input
                            value={seo.ogType || ""}
                            onChange={(e) => handleChange("ogType", e.target.value)}
                            placeholder="OG Type"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>OG URL</Label>
                        <Input
                            value={seo.ogUrl || ""}
                            onChange={(e) => handleChange("ogUrl", e.target.value)}
                            placeholder="https://example.com"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>OG Site Name</Label>
                        <Input
                            value={seo.ogSiteName || ""}
                            onChange={(e) => handleChange("ogSiteName", e.target.value)}
                            placeholder="OG Site Name"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Twitter Title</Label>
                        <Input
                            value={seo.twitterTitle || ""}
                            onChange={(e) => handleChange("twitterTitle", e.target.value)}
                            placeholder="Twitter Title"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Twitter Description</Label>
                        <Input
                            value={seo.twitterDescription || ""}
                            onChange={(e) => handleChange("twitterDescription", e.target.value)}
                            placeholder="Twitter Description"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Twitter Image URL</Label>
                        <Input
                            value={seo.twitterImage || ""}
                            onChange={(e) => handleChange("twitterImage", e.target.value)}
                            placeholder="https://example.com/image.jpg"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Twitter Card</Label>
                        <Input
                            value={seo.twitterCard || ""}
                            onChange={(e) => handleChange("twitterCard", e.target.value)}
                            placeholder="Twitter Card"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Robots Tag</Label>
                        <Input
                            value={seo.robots || ""}
                            onChange={(e) => handleChange("robots", e.target.value)}
                            placeholder="Robots Tag"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Language</Label>
                        <Input
                            value={seo.language || ""}
                            onChange={(e) => handleChange("language", e.target.value)}
                            placeholder="Language"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Structured Data</Label>
                        <Input
                            value={seo.structuredData || ""}
                            onChange={(e) => handleChange("structuredData", e.target.value)}
                            placeholder="Structured Data"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Sitemap Priority</Label>
                        <Input
                            value={seo.sitemapPriority || ""}
                            onChange={(e) => handleChange("sitemapPriority", e.target.value)}
                            placeholder="Sitemap Priority"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Change Frequency</Label>
                        <Input
                            value={seo.changefreq || ""}
                            onChange={(e) => handleChange("changefreq", e.target.value)}
                            placeholder="Change Frequency"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div>
                        <Label>Custom Meta Tags</Label>
                        <Input
                            value={seo.customMetaTags || ""}
                            onChange={(e) => handleChange("customMetaTags", e.target.value)}
                            placeholder="Custom Meta Tags"
                            className="border-black/20 rounded-none"
                        />
                    </div>

                    <div className="flex gap-4 mt-6 justify-end">
                        <Button onClick={() => router.back()} variant="outline">
                            Cancel
                        </Button>
                        <Button onClick={handleSave} disabled={updateMutation.isPending}>
                            {updateMutation.isPending ? "Saving..." : "Save Changes"}
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
