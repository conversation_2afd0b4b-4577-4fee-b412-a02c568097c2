import { useQuery } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { IPackageReview } from '@/types/package_';

export function useGetPackageReviewById(id: string) {
  return useQuery<IApiResponse<IPackageReview[]>, Error>({
    queryKey: ['packages-review', id],
    queryFn: () =>
      fetch(`/api/package-review/package/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
