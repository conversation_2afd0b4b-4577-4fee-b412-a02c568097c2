import { IPackageGroupPrice } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdatePackageGroupPrice(id: string) {
  const queryClient = useQueryClient();
  return useMutation<
    IApiResponse<IPackageGroupPrice>,
    Error,
    IPackageGroupPrice
  >({
    mutationFn: (data: IPackageGroupPrice) =>
      fetch(`/api/package-group-price/${data.id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-group-price'] });
      queryClient.invalidateQueries({ queryKey: ['packages-group-price', id] });
    },
    onError: () => {
      toast.error('Error updating package');
    },
  });
}
