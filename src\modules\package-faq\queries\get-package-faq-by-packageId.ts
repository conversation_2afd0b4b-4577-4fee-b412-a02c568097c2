import { useQuery } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { IPackageFaq } from '@/types/package_';

export function useGetPackageFaqById(id: string) {
  return useQuery<IApiResponse<IPackageFaq[]>, Error>({
    queryKey: ['packages-faq', id],
    queryFn: () =>
      fetch(`/api/package-faq/package/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
