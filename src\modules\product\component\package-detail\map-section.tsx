import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { IPackageMap } from "@/types/package_";
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image";
import Image from "next/image";

interface PackageMapFormProps {
  map?: Partial<IPackageMap>;
  onMapChange: (data: Partial<IPackageMap>) => void;
}

export function PackageMapForm({ map, onMapChange }: PackageMapFormProps) {
  const handleChange = (field: keyof IPackageMap, value: string) => {
    onMapChange({ ...map, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Trek Route Map</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Label>Title</Label>
        <Input
          value={map?.title || ""}
          onChange={e => handleChange("title", e.target.value)}
          placeholder="Map title"
        />
        <Label>Map Image</Label>
        <ImageUploadSingleWithMutation
          label="Map Image"
          accept="image/*"
          initialUrl={map?.map || ""}
          onUploaded={url => handleChange("map", url)}
        />
        {map?.map && (
          <div className="mt-2">
            <p className="text-sm font-medium mb-1">Uploaded Map Preview:</p>
            <Image
              src={map.map}
              alt="Preview"
              width={160}
              height={100}
              className="w-40 h-24 object-cover rounded border mb-2"
            />
          </div>
        )}

      </CardContent>
    </Card>
  );
}
