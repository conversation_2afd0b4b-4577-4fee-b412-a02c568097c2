import { IPackageGroupPrice } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageGroupPrices(params?: {
  page?: number;
  limit?: number;
  sort?: string;
  searchFields?: string;
  search?: string;
}) {
  return useQuery<IApiResponse<IPackageGroupPrice[]>, Error>({
    queryKey: ['packages-group-price', params],
    queryFn: () =>
      fetch(
        `/api/package-group-price?${new URLSearchParams(
          params as Record<string, string>
        )}`,
        {
          mode: 'cors',
          credentials: 'include',
        }
      ).then((res) => res.json()),
  });
}
