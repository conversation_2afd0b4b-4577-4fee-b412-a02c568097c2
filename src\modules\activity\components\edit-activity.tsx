"use client";

import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useUpdateActivity } from "../mutation/use-update-activity";
import { IActivity } from "@/types/package_";
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image";

interface EditActivityProps {
  initialActivity: IActivity;
}

const EditActivity: React.FC<EditActivityProps> = ({ initialActivity }) => {
  const [activity, setActivity] = useState<IActivity>(initialActivity);
  const updateActivity = useUpdateActivity();
  const router = useRouter();

  useEffect(() => {
    setActivity(initialActivity);
  }, [initialActivity]);

  const handleChange = (field: keyof IActivity, value: string) => {
    setActivity({ ...activity, [field]: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateActivity.mutate(activity, {
      onSuccess: () => {
        toast.success("Activity updated successfully");
        router.push("/activities-offer");
      },
      onError: (err: Error) => {
        toast.error(`Update failed: ${err.message}`);
      },
    });
  };

  return (
    <div className="p-6 container mx-auto bg-white rounded shadow">
      <h1 className="text-2xl font-bold mb-6">Edit Activity</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="name" className="block mb-1 font-medium">Name</label>
          <input
            id="name"
            type="text"
            value={activity.name}
            onChange={(e) => handleChange("name", e.target.value)}
            className="w-full border rounded px-3 py-2"
            required
          />
        </div>

        <div>
          <label htmlFor="slug" className="block mb-1 font-medium">Slug</label>
          <input
            id="slug"
            type="text"
            value={activity.slug}
            onChange={(e) => handleChange("slug", e.target.value)}
            className="w-full border rounded px-3 py-2"
            required
          />
        </div>

        <div>
          <label className="block mb-1 font-medium">Image</label>
          <ImageUploadSingleWithMutation
            initialUrl={activity.image}
            onUploaded={(url) => handleChange("image", url)}
          />
        </div>

        <div>
          <label htmlFor="description" className="block mb-1 font-medium">Description</label>
          <textarea
            id="description"
            value={activity.description}
            onChange={(e) => handleChange("description", e.target.value)}
            rows={4}
            className="w-full border rounded px-3 py-2"
            required
          ></textarea>
        </div>

        <label htmlFor="showOnNav" className="flex items-center gap-2 mb-1 font-medium">
          <input
            id="showOnNav"
            type="checkbox"
            checked={Boolean(activity.showOnNav)} 
            onChange={(e) =>
              setActivity({ ...activity, showOnNav: e.target.checked })
            }
            className="accent-blue-600"
          />
          Show on Nav
        </label>


        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
        >
          Update Activity
        </button>
      </form>
    </div>
  );
};

export default EditActivity;
