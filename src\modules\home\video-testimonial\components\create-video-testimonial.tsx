'use client';

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ITestimonial, ITestimonialItem } from "@/types/home";
import { UseCreateTestimonial } from "../mutations/use-create-testimonial";
import { useGetHome } from "../../queries/get-home";
import { toast } from "sonner";
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image";

const CreateVideoTestimonial: React.FC = () => {
  const router = useRouter();
  const createTestimonial = UseCreateTestimonial();
  const { data: homeData } = useGetHome();

  const [testimonialBlock, setTestimonialBlock] = useState<ITestimonial>({
    id: "",
    homeId: "",
    title: "",
    subtitle: "",
    createdAt: "",
    updatedAt: "",
    testimonials: [
      {
        id: "",
        homeVideoTestimonialId: "",
        youtubeUrl: "",
        youtubeThumbnail: "",
        title: "",
        destination: "",
        date: "",
        createdAt: "",
      },
    ],
  });

  useEffect(() => {
    if (homeData?.data?.id) {
      const id = homeData.data.id;
      setTestimonialBlock((prev) => ({
        ...prev,
        homeId: id,
        testimonials: prev.testimonials.map((t) => ({
          ...t,
          homeVideoTestimonialId: id,
        })),
      }));
    }
  }, [homeData]);


  const handleBlockFieldChange = (field: keyof ITestimonial, value: string) => {
    setTestimonialBlock((prev) => ({ ...prev, [field]: value }));
  };

  const handleTestimonialChange = (
    index: number,
    field: keyof ITestimonialItem,
    value: string
  ) => {
    const newTestimonials = [...testimonialBlock.testimonials];
    newTestimonials[index] = { ...newTestimonials[index], [field]: value };
    setTestimonialBlock((prev) => ({ ...prev, testimonials: newTestimonials }));
  };

  const handleAddTestimonial = () => {
    setTestimonialBlock((prev) => ({
      ...prev,
      testimonials: [
        ...prev.testimonials,
        {
          id: '',
          homeVideoTestimonialId: prev.homeId,
          youtubeUrl: '',
          youtubeThumbnail: '',
          title: '',
          destination: '',
          date: '',
          createdAt: '',
        },
      ],
    }));
  };

  const handleDeleteTestimonial = (index: number) => {
    setTestimonialBlock((prev) => ({
      ...prev,
      testimonials: prev.testimonials.filter((_, i) => i !== index),
    }));
  };

  const handleSave = () => {
    if (!testimonialBlock.homeId) {
      toast.error('Home ID is required');
      return;
    }
    createTestimonial.mutate(testimonialBlock, {
      onSuccess: () => {
        toast.success("Video testimonial created successfully");
        router.push("/home");
        toast.success('Video testimonial created successfully');
        router.push('/home');
      },
      onError: () => {
        toast.error('Error creating video testimonial');
      },
    });
  };

  if (!homeData?.data?.id) {
    return <p className="p-6">Loading Home Data...</p>;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">Create Video Testimonials</h1>

      <div>
        <label className="block mb-1 font-medium">Heading</label>
        <input
          type="text"
          value={testimonialBlock.title}
          onChange={(e) => handleBlockFieldChange('title', e.target.value)}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <label className="block mb-1 font-medium">Subheading</label>
        <textarea
          value={testimonialBlock.subtitle}
          onChange={(e) => handleBlockFieldChange('subtitle', e.target.value)}
          rows={3}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-4">Testimonials</h2>
        {testimonialBlock.testimonials.map((t, index) => (
          <div
            key={index}
            className="mb-4 border rounded p-4 bg-white space-y-3"
          >
            <input
              type="text"
              placeholder="YouTube URL"
              value={t.youtubeUrl}
              onChange={(e) =>
                handleTestimonialChange(index, 'youtubeUrl', e.target.value)
              }
              className="w-full rounded border px-3 py-2"
            />
            <ImageUploadSingleWithMutation
              label="Upload YouTube Thumbnail"
              initialUrl={t.youtubeThumbnail}
              onUploaded={(url) =>
                handleTestimonialChange(index, 'youtubeThumbnail', url)
              }
            />
            <input
              type="text"
              placeholder="Title"
              value={t.title}
              onChange={(e) =>
                handleTestimonialChange(index, 'title', e.target.value)
              }
              className="w-full rounded border px-3 py-2"
            />
            <input
              type="text"
              placeholder="Destination"
              value={t.destination}
              onChange={(e) =>
                handleTestimonialChange(index, 'destination', e.target.value)
              }
              className="w-full rounded border px-3 py-2"
            />
            <input
              type="date"
              value={t.date}
              onChange={(e) =>
                handleTestimonialChange(index, 'date', e.target.value)
              }
              className="w-full rounded border px-3 py-2"
            />
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteTestimonial(index)}
              className="mt-2"
            >
              Delete Testimonial
            </Button>
          </div>
        ))}
        <Button onClick={handleAddTestimonial} className="mt-3">
          + Add Testimonial
        </Button>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          onClick={handleSave}
          className="bg-brand text-white hover:bg-brand/80"
        >
          Create Video Testimonials
        </Button>
      </div>
    </div>
  );
};

export default CreateVideoTestimonial;
