import { IPackageEquipment } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageEquipmentById(id: string) {
  return useQuery<IApiResponse<IPackageEquipment>, Error>({
    queryKey: ['packages-equipment', id],
    queryFn: () =>
      fetch(`/api/package-equipment/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
