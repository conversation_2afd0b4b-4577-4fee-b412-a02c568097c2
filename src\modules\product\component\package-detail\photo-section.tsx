'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { IPackageGallery, IPackageYtVideo, IPackageGalleryImage } from "@/types/package_"
import { Trash2, Plus } from "lucide-react"
import ImageUploadSingleWithMutation from "@/modules/images/components/upload-single-image"

interface PackageGalleryVideoFormProps {
  gallery?: Partial<IPackageGallery> | undefined;
  ytVideo?: Partial<IPackageYtVideo> | undefined;
  onGalleryChange: (gallery: Partial<IPackageGallery>) => void;
  onYtVideoChange: (ytVideo: Partial<IPackageYtVideo>) => void;
}

export function PackageGalleryVideoForm({
  gallery,
  ytVideo,
  onGalleryChange,
  onYtVideoChange
}: PackageGalleryVideoFormProps) {

  const addVideoLink = () => {
    const links = [...(ytVideo?.links || []), ''];
    onYtVideoChange({
      ...ytVideo,
      links
    });
  };

  const removeVideoLink = (index: number) => {
    const links = (ytVideo?.links || []).filter((_, i) => i !== index);
    onYtVideoChange({
      ...ytVideo,
      links
    });
  };

  const handleVideoLinkChange = (index: number, value: string) => {
    const links = [...(ytVideo?.links || [])];
    links[index] = value;
    onYtVideoChange({
      ...ytVideo,
      links
    });
  };

  const addGalleryImage = () => {
    const currentImages = gallery?.PackageGalleryImage || [];
    const newImage = {
      id: '', // Empty string as before
      packageGalleryId: gallery?.id || '',
      src: '',
      caption: '',
      createdAt: '',
      updatedAt: ''
    };
    
    onGalleryChange({
      ...gallery,
      PackageGalleryImage: [...currentImages, newImage]
    });
  };

  const removeGalleryImage = (index: number) => {
    const currentImages = gallery?.PackageGalleryImage || [];
    const updatedImages = currentImages.filter((_, i) => i !== index);
    onGalleryChange({
      ...gallery,
      PackageGalleryImage: updatedImages
    });
  };

  const handleGalleryImageChange = (index: number, field: keyof IPackageGalleryImage, value: string) => {
    const currentImages = [...(gallery?.PackageGalleryImage || [])];
    currentImages[index] = { 
      ...currentImages[index], 
      [field]: value
      // Don't manually set updatedAt - let backend handle it
    };
    onGalleryChange({
      ...gallery,
      PackageGalleryImage: currentImages
    });
  };

  console.log('Gallery images:', gallery?.PackageGalleryImage);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Gallery */}
      <Card>
        <CardHeader>
          <CardTitle>Photo Gallery</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="gallery-title">Gallery Title</Label>
            <Input
              id="gallery-title"
              value={gallery?.title || ''}
              onChange={(e) => onGalleryChange({ ...gallery, title: e.target.value })}
              placeholder="Photo Gallery"
              className="border-black/20 rounded-none"
            />
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <Label>Gallery Images</Label>
              <Button
                type="button"
                onClick={addGalleryImage}
                size="sm"
                variant="outline"
                className="border-black/20 rounded-none"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Image
              </Button>
            </div>

            <div className="space-y-4">
              {(gallery?.PackageGalleryImage || []).map((image, index) => (
                <div key={`image-${index}-${image.src || 'empty'}`} className="border p-4 rounded space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Image {index + 1}</Label>
                    <Button
                      type="button"
                      onClick={() => removeGalleryImage(index)}
                      size="sm"
                      variant="outline"
                      className="border-red-300 text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                  <ImageUploadSingleWithMutation
                    label="Image"
                    accept="image/*"
                    initialUrl={image.src}
                    onUploaded={(url) => handleGalleryImageChange(index, 'src', url)}
                  />
                  <Input
                    value={image.caption || ''}
                    onChange={(e) => handleGalleryImageChange(index, 'caption', e.target.value)}
                    placeholder="Image caption"
                    className="border-black/20 rounded-none"
                  />
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* YouTube Videos */}
      <Card>
        <CardHeader>
          <CardTitle>YouTube Videos</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="video-title">Video Title</Label>
            <Input
              id="video-title"
              value={ytVideo?.title || ''}
              onChange={(e) => onYtVideoChange({ ...ytVideo, title: e.target.value })}
              placeholder="Trek Videos"
              className="border-black/20 rounded-none"
            />
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <Label>Video Links</Label>
              <Button
                type="button"
                onClick={addVideoLink}
                size="sm"
                variant="outline"
                className="border-black/20 rounded-none"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Video
              </Button>
            </div>

            <div className="space-y-2">
              {(ytVideo?.links || []).map((link, index) => (
                <div key={index} className="flex gap-2">
                  <Input
                    value={link}
                    onChange={(e) => handleVideoLinkChange(index, e.target.value)}
                    placeholder="https://youtube.com/watch?v=..."
                    className="border-black/20 rounded-none flex-1"
                  />
                  <Button
                    type="button"
                    onClick={() => removeVideoLink(index)}
                    size="sm"
                    variant="outline"
                    className="border-red-300 text-red-600 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
            {(ytVideo?.links || []).length === 0 && (
              <div className="text-center py-4 text-gray-500">
                No video links added yet. Click &quot;Add Video Link&quot; to
                start.
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}