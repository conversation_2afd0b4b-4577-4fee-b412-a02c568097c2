import { IPackageItinerary } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeletePackageItinerary(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageItinerary>, Error, any>({
    mutationFn: (itineraryId: string) =>
      fetch(`/api/package-itinerary/${itineraryId}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-itinerary'] });
      toast.success('Package deleted successfully');
    },
    onError: () => {
      toast.error('Error deleting package');
    },
  });
}
