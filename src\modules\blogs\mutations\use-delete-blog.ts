import { IBlog } from '@/types/blogs';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeleteBlog(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IBlog>, Error, string>({
    mutationFn: (id: string) =>
      fetch(`/api/blogs/${id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      toast.success('Blog deleted successfully');
    },
    onError: () => {
      toast.error('Error deleting Blog');
    },
  });
}
