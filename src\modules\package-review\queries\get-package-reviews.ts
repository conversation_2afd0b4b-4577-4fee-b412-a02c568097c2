import { IPackageReview } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageReviews(params?: {
  page?: number;
  limit?: number;
  sort?: string;
  searchFields?: string;
  search?: string;
}) {
  return useQuery<IApiResponse<IPackageReview[]>, Error>({
    queryKey: ['packages-review', params],
    queryFn: () =>
      fetch(
        `/api/package-review?${new URLSearchParams(
          params as Record<string, string>
        )}`,
        {
          mode: 'cors',
          credentials: 'include',
        }
      ).then((res) => res.json()),
  });
}
