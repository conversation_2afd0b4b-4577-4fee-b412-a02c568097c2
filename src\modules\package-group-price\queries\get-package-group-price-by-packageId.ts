import { useQuery } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { IPackageGroupPrice } from '@/types/package_';

export function useGetPackageGroupPriceById(id: string) {
  return useQuery<IApiResponse<IPackageGroupPrice[]>, Error>({
    queryKey: ['packages-group-price', id],
    queryFn: () =>
      fetch(`/api/package-group-price/package/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
