import { IPackageCostDate } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageCostDates(params?: {
  page?: number;
  limit?: number;
  sort?: string;
  searchFields?: string;
  search?: string;
}) {
  return useQuery<IApiResponse<IPackageCostDate[]>, Error>({
    queryKey: ['packages-cost-date', params],
    queryFn: () =>
      fetch(
        `/api/package-cost-date?${new URLSearchParams(
          params as Record<string, string>
        )}`,
        {
          mode: 'cors',
          credentials: 'include',
        }
      ).then((res) => res.json()),
  });
}
