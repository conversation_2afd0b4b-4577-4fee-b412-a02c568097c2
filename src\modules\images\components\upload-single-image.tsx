"use client";

import React, { useRef, useState } from "react";
import { useUploadSingleImage } from "../mutations/use-upload-single-image";
import Image from "next/image";

interface ImageUploadSingleWithMutationProps {
    onUploaded: (imageUrl: string) => void;
    label?: string;
    initialUrl?: string;
    accept?: string;
}

const ImageUploadSingleWithMutation: React.FC<ImageUploadSingleWithMutationProps> = ({
    onUploaded,
    label = "Upload Image",
    initialUrl = "",
    accept = "image/*",
}) => {
    const inputRef = useRef<HTMLInputElement | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string>(initialUrl);
    const uploadMutation = useUploadSingleImage();

    const handleClick = () => {
        inputRef.current?.click();
    };

    const MAX_FILE_SIZE = 5 * 1024 * 1024;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        setPreviewUrl(URL.createObjectURL(file));

        if (file.size > MAX_FILE_SIZE) {
            alert("File size exceeds 5MB limit. Please select a smaller file.");
            setPreviewUrl(initialUrl);
            e.target.value = "";
            return;
        }

        uploadMutation.mutate(file, {
            onSuccess: (result) => {
                if (result.success && result.data && result.data.url) {
                    const url = result.data.url;
                    setPreviewUrl(url);
                    onUploaded(url);
                } else {
                    alert("Upload failed");
                    setPreviewUrl(initialUrl);
                }
            },
            onError: () => {
                alert("Error uploading image");
                setPreviewUrl(initialUrl);
            },
        });

    };

    return (
        <div className="flex flex-col items-center gap-2">
            {label && <p className="mb-2 font-medium">{label}</p>}

            {previewUrl && (
                <Image
                    src={previewUrl}
                    alt="Preview"
                    width={160}
                    height={100}
                    className="w-40 h-24 object-cover rounded border mb-2"
                />
            )}

            <input
                ref={inputRef}
                type="file"
                accept={accept}
                onChange={handleChange}
                className="hidden"
            />
            <button
                type="button"
                onClick={handleClick}
                className="bg-brand text-white px-3 py-1 rounded hover:bg-brand/80"
            >
                Upload
            </button>

            <p className="text-xs text-gray-800 mt-1">
                  Max: 10MB video, 500KB image, 4MB pdf
            </p>

        </div>
    );
};

export default ImageUploadSingleWithMutation;
