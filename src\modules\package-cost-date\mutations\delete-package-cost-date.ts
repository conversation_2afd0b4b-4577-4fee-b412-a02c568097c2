import { IPackageCostDate } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useDeletePackageCostDate(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageCostDate>, Error, any>({
    mutationFn: (costDateId: string) =>
      fetch(`/api/package-cost-date/${costDateId}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-cost-date'] });
      toast.success('Package cost date deleted successfully');
    },
    onError: () => {
      toast.error('Error deleting package cost date');
    },
  });
}
