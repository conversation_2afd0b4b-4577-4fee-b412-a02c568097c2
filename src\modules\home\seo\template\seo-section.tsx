'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

interface HomeSEOSectionProps {
    seoData: {
        id: string;
        metaTitle?: string | null;
        metaDescription?: string | null;
        metaKeywords?: string[] | null;
    };
}

const displayOrNA = (value?: string | null) => (value && value.trim() ? value : 'N/A');

export default function HomeSEOSection({ seoData }: HomeSEOSectionProps) {
    const router = useRouter();

    const handleUpdateClick = () => {
        router.push(`/home/<USER>/edit`);
    };

    return (
        <div>
            <section className="p-6">
                <div className="bg-white rounded-lg border border-black/20 shadow-lg p-6 max-w-full mx-auto">
                    <div className="flex items-center justify-between mb-6">
                        <h2 className="text-2xl font-bold mb-4">Home SEO Summary</h2>
                        <Button onClick={handleUpdateClick}>Edit Seo Detail</Button>
                    </div>
                    <table className="min-w-full border border-gray-300 rounded">
                        <thead>
                            <tr className="bg-gray-100">
                                <th className="border p-2 text-left w-48 font-medium">Meta Title</th>
                                <th className="border p-2 text-left font-medium">Meta Description</th>
                                <th className="border p-2 text-left font-medium">Meta Keywords</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td className="border p-2">{displayOrNA(seoData.metaTitle)}</td>
                                <td className="border p-2">{displayOrNA(seoData.metaDescription)}</td>
                                <td className="border p-2">
                                    {seoData.metaKeywords && seoData.metaKeywords.length > 0
                                        ? seoData.metaKeywords.join(', ')
                                        : 'N/A'}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
    );
}
