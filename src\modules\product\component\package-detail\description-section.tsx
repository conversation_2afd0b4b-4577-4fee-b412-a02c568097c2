'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import dynamic from "next/dynamic"
import { IPackageDescription } from "@/types/package_"

const RichTextEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false })

interface PackageDescriptionFormProps {
  description?: Partial<IPackageDescription> | undefined;
  onDescriptionChange: (description: Partial<IPackageDescription>) => void;
}

export function PackageDescriptionForm({ description, onDescriptionChange }: PackageDescriptionFormProps) {
  const handleChange = (field: keyof IPackageDescription, value: string) => {
    onDescriptionChange({
      ...description,
      [field]: value
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Package Description</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="description-title">Title</Label>
          <Input
            id="description-title"
            value={description?.title || ''}
            onChange={(e) => handleChange('title', e.target.value)}
            placeholder="Enter description title"
            className="w-full border-black/20 rounded-none"
          />
        </div>
        <div>
          <Label htmlFor="description-content">Description</Label>
          <div className="mt-1">
            <RichTextEditor
              value={description?.description || ''}
              onChange={(data) => handleChange('description', data)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
