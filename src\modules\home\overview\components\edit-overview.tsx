'use client';

import React, { useState, useEffect, ChangeEvent } from 'react';
import { IOverview, IOverviewPoints } from '@/types/home';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useGetHome } from '../../queries/get-home';
import { UseGetOverview } from '../queries/use-get-overview';
import { UseUpdateOverview } from '../mutations/use-update-overview';
import ImageUploadSingleWithMutation from '@/modules/images/components/upload-single-image';

const EditOverviewPage: React.FC = () => {
  const router = useRouter();
  const { data: homeData } = useGetHome();
  const { data, isLoading, error } = UseGetOverview();
  const updateOverview = UseUpdateOverview();

  const [overview, setOverview] = useState<IOverview | null>(null);

  useEffect(() => {
    if (data?.data) {
      setOverview(data.data);
    }
  }, [data]);

  useEffect(() => {
    const homeId = homeData?.data?.id;
    if (homeId && overview && !overview.homeId) {
      setOverview((prev) => prev && { ...prev, homeId });
    }
  }, [homeData, overview]);

  const handleHeadingChange = (e: ChangeEvent<HTMLInputElement>) => {
    setOverview((prev) => prev && { ...prev, heading: e.target.value });
  };

  const handleDescriptionChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setOverview((prev) => prev && { ...prev, description: e.target.value });
  };

  const handleFeatureChange = (
    index: number,
    field: keyof IOverviewPoints,
    value: string
  ) => {
    setOverview((prev) => {
      if (!prev) return prev;
      const points = [...prev.points];
      points[index] = { ...points[index], [field]: value };
      return { ...prev, points };
    });
  };

  const handleAddFeature = () => {
    setOverview((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        points: [...prev.points, { id: '', title: '', icon: '' }],
      };
    });
  };

  const handleDeleteFeature = (index: number) => {
    setOverview((prev) => {
      if (!prev) return prev;
      return { ...prev, points: prev.points.filter((_, i) => i !== index) };
    });
  };

  const handleSave = () => {
    if (overview && overview.id) {
      updateOverview.mutate(overview, {
        onSuccess: () => {
          router.push('/home');
        },
        onError: (err) => {
          alert(`Update failed: ${err.message}`);
        },
      });
    } else {
      alert('Overview ID missing, cannot update.');
    }
  };

  if (isLoading || !overview) return <p>Loading overview...</p>;
  if (error) return <p>Error loading overview: {error.message}</p>;

  return (
    <div className="p-6 container mx-auto space-y-6">
      <h1 className="text-3xl font-bold mb-4">Edit Overview</h1>

      <div>
        <label className="block mb-1 font-medium">Heading</label>
        <input
          type="text"
          value={overview.heading}
          onChange={handleHeadingChange}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <label className="block mb-1 font-medium">Description</label>
        <textarea
          value={overview.description}
          onChange={handleDescriptionChange}
          rows={3}
          className="w-full rounded border px-3 py-2"
        />
      </div>
      <div>
        <label className="block mb-1 font-medium">Link URL</label>
        <input
          type="text"
          value={overview.linkUrl}
          onChange={(e) => setOverview((prev) => prev && { ...prev, linkUrl: e.target.value })}
          className="w-full rounded border px-3 py-2"
        />
      </div>
      <div>
        <label className="block mb-1 font-medium">Link Label</label>
        <input
          type="text"
          value={overview.linkLabel}
          onChange={(e) => setOverview((prev) => prev && { ...prev, linkLabel: e.target.value })}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-3">Points</h2>
        {overview.points.map((feature, index) => (
          <div key={index} className="mb-4 border rounded p-4 bg-white">
            <input
              type="text"
              placeholder="Feature Title"
              value={feature.title}
              onChange={(e) =>
                handleFeatureChange(index, 'title', e.target.value)
              }
              className="w-full mb-2 rounded border px-3 py-2"
            />
            <ImageUploadSingleWithMutation
              label="Upload Feature Icon"
              initialUrl={feature.icon}
              onUploaded={(url) => handleFeatureChange(index, 'icon', url)}
              accept="image/*"
            />
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteFeature(index)}
              className="mt-2"
            >
              Delete Point
            </Button>
          </div>
        ))}
        {overview.points.length < 4 && (
          <Button onClick={handleAddFeature} className="mt-3">
            + Add Point
          </Button>
        )}
      </div>

      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          className="bg-brand text-white hover:bg-brand/80"
        >
          Update Overview
        </Button>
      </div>
    </div>
  );
};

export default EditOverviewPage;
