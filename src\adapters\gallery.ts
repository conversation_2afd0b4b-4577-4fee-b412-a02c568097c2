import { IPackageGallery, IPackageGalleryImage } from "@/types/package_";

export function normalizeGallery(
  apiGallery?: Partial<IPackageGallery> & {
    images?: { src?: string; caption?: string }[];
    PackageGalleryImage?: IPackageGalleryImage[];
  }
): Partial<IPackageGallery> {
  if (!apiGallery) return {};

  const fromRelation = apiGallery.PackageGalleryImage ?? [];
  const fromImages =
    (apiGallery.images ?? []).map((g): IPackageGalleryImage => ({
      id: "",
      packageGalleryId: apiGallery.id ?? "",
      src: g.src ?? "",
      caption: g.caption ?? "",
      createdAt: "",
      updatedAt: "",
    })) ?? [];

  const images: IPackageGalleryImage[] =
    fromRelation.length > 0 ? fromRelation : fromImages;

  return {
    id: apiGallery.id,
    title: apiGallery.title ?? "Photo Gallery",
    PackageGalleryImage: images,
  };
}

export function denormalizeGallery(
  stateGallery?: Partial<IPackageGallery>
): Partial<IPackageGallery> {
  const list = stateGallery?.PackageGalleryImage ?? [];
  return {
    id: stateGallery?.id,
    title: stateGallery?.title ?? "",
    PackageGalleryImage: [], 
    createdAt: "",
    updatedAt: "",
    packageId: stateGallery?.id ?? "",
    images: list.map((it) => ({
      src: it.src ?? "",
      caption: it.caption ?? "",
    })),
  } as Partial<IPackageGallery>;
}

