'use client';

import React, { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { toast } from 'sonner';
import { EditTabs } from '@/modules/product/component/edit-tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

import { IPackageUpcomingStatus } from '@/types/package_';
import { useUpdatePackageUpcomingStatus } from '@/modules/package-upcoming-status/mutations/use-update-upcoming-status';
import { useGetPackageUpcomingStatusById } from '@/modules/package-upcoming-status/queries/use-get-upcoming-status';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCreatePackageUpcomingStatus } from '@/modules/package-upcoming-status/mutations/use-create-upcoming-status';

const initialUpcomingStatus = (packageId: string): Omit<IPackageUpcomingStatus, 'id' | 'createdAt' | 'updatedAt'> => ({
    packageId,
    departureDateEnd: '',
    departureDateStart: '',
    status: 'WAITING',
});


export default function EditPackageUpcomingStatusPage() {
    const { id: packageId } = useParams() as { id: string };

    const { data, isLoading, isError } = useGetPackageUpcomingStatusById(packageId);
    const updateMutation = useUpdatePackageUpcomingStatus(packageId);
    const createMutation = useCreatePackageUpcomingStatus();

    const [upcomingStatus, setUpcomingStatus] = useState<Omit<IPackageUpcomingStatus, 'id' | 'createdAt' | 'updatedAt'>>(initialUpcomingStatus(packageId));

    const [upcomingStatusId, setUpcomingStatusId] = useState<string>('');

    useEffect(() => {
        if (data?.data && data.data.id) {
            setUpcomingStatusId(data.data.id);
            setUpcomingStatus({
                ...initialUpcomingStatus(packageId),
                ...data.data,
                packageId,
            });
        }
    }, [data, packageId]);

    const handleChange = (field: keyof typeof upcomingStatus, value: string) => {
        setUpcomingStatus((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    const handleSave = () => {
        if (upcomingStatusId) {
            updateMutation.mutate(
                { ...upcomingStatus, id: packageId },
                {
                    onSuccess: () => {
                        toast.success('Upcoming Status updated successfully');
                    },
                    onError: (error) => {
                        toast.error(`Failed to update Upcoming Status: ${error.message}`);
                    },
                }
            );
        } else {
            createMutation.mutate(
                { ...upcomingStatus, packageId },
                {
                    onSuccess: () => {
                        toast.success('Upcoming Status created successfully');
                    },
                    onError: (error) => {
                        toast.error(`Failed to create Upcoming Status: ${error.message}`);
                    },
                }
            );
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        handleSave();
    };

    if (isLoading) return <div>Loading Upcoming Status data...</div>;
    if (isError) return <div>Error loading Upcoming Status data.</div>;

    return (
        <div className="min-h-screen p-6 bg-gray-50">
            <EditTabs packageId={packageId} />
            <Card>
                <CardHeader>
                    <CardTitle>Package Upcoming Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 max-w-3xl">
                    <form onSubmit={handleSubmit}>
                        <div className='mb-4'>
                            <Label className="mb-2">Departure Date Start</Label>
                            <Input
                                type='date'
                                value={upcomingStatus.departureDateStart ?? ''}
                                onChange={(e) => handleChange('departureDateStart', e.target.value)}
                                placeholder="Departure Date Start"
                                className="border-black/20 rounded-none"
                                required
                            />
                        </div>
                        <div className='mb-4'>
                            <Label className="mb-2">Departure Date End</Label>
                            <Input
                                type='date'
                                value={upcomingStatus.departureDateEnd ?? ''}
                                onChange={(e) => handleChange('departureDateEnd', e.target.value)}
                                placeholder="Departure Date End"
                                className="border-black/20 rounded-none"
                                required
                            />
                        </div>
                        <div className='mb-4'>
                            <Label className="mb-2">Status</Label>
                            <Select
                                value={upcomingStatus.status ?? ''}
                                onValueChange={(value) => handleChange('status', value)}
                            >
                                <SelectTrigger className="border-black/20 rounded-none">
                                    <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="WAITING">WAITING</SelectItem>
                                    <SelectItem value="GUARANTEED">GUARANTEED</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="flex gap-4 justify-end pt-4 border-t">
                            <Button onClick={handleSave}>Save</Button>
                        </div>
                    </form>
                </CardContent>
            </Card>
        </div>
    );
}
