import { IPackageUpcomingStatus } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreatePackageUpcomingStatus() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageUpcomingStatus>, Error, any>({
    mutationFn: (data: any) =>
      fetch(`/api/package-upcoming/${data.packageId}`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-upcoming-status'] });
    },
    onError: () => {
      toast.error('Error creating package');
    },
  });
}
