"use client";

import React from "react";
import { useRouter } from "next/navigation"; 
import { UseGetOverview } from "../queries/use-get-overview";

const OverviewDisplay: React.FC = () => {
  const { data, isLoading, error } = UseGetOverview();
  const router = useRouter();

  if (isLoading) return <p>Loading overview...</p>;
  if (error) return <p>Error loading overview: {error.message}</p>;

  const overview = data?.data;

  return (
    <div className="p-6 container mx-auto text-center">
      {overview ? (
        <>
          <h1 className="text-3xl font-bold mb-4">{overview.heading}</h1>
          <p className="mb-6">{overview.description}</p>

          <table className="min-w-full border border-gray-300 mb-6">
            <thead>
              <tr className="bg-gray-100">
                <th className="border px-4 py-2">Point Title</th>
                <th className="border px-4 py-2">Point Icon</th>
              </tr>
            </thead>
            <tbody>
              {overview.points.map((feature, idx) => (
                <tr key={idx} className="even:bg-gray-50">
                  <td className="border px-4 py-2">{feature.title}</td>
                  <td className="border px-4 py-2">{feature.icon}</td>
                </tr>
              ))}
            </tbody>
          </table>

          <button
            className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
            onClick={() => router.push("/home/<USER>/edit")}
          >
            Edit 
          </button>
        </>
      ) : (
        <>
          <p className="mb-6">No overview data found.</p>
          <button
            className="bg-brand text-white px-4 py-2 rounded hover:bg-brand/80"
            onClick={() => router.push("/home/<USER>/create")}
          >
            Create Overview
          </button>
        </>
      )}
    </div>
  );
};

export default OverviewDisplay;
