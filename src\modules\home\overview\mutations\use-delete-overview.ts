import { IOverview } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function UseDeleteOverview() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IOverview>, Error, IOverview>({
    mutationFn: (data: IOverview) =>
      fetch(`/api/home-overview/${data.id}`, {
        method: 'DELETE',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['overview'] });
      toast.success('Overview Deleted Sucessfully');
    },
    onError: () => {
      toast.error('Error Deleting Overview');
    },
  });
}
