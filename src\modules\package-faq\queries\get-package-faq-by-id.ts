import { IPackage } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageFaqById(id: string) {
  return useQuery<IApiResponse<IPackage>, Error>({
    queryKey: ['packages-faq', id],
    queryFn: () =>
      fetch(`/api/package-faq/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
