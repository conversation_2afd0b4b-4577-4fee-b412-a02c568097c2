'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Mountain, ChevronLeft, ChevronRight, ChevronDown } from 'lucide-react';

type NavItem = {
  title: string;
  href?: string;
  children?: NavItem[];
};

export default function Sidebar() {
  const [collapsed, setCollapsed] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const pathname = usePathname();

  const toggleExpand = (title: string) => {
    setExpandedItems((prev) =>
      prev.includes(title) ? prev.filter((t) => t !== title) : [...prev, title]
    );
  };

  const menuItems: NavItem[] = [
    {
      title: 'Home Content', href: '/home',
    },
    {
      title: 'Package',
      children: [
        { title: 'Create Package', href: '/package/create' },
        { title: 'List Package', href: '/package' },
      ],
    },
    {
      title: 'Operations',
      children: [
        { title: 'Bookings', href: '/bookings' },
        { title: 'Customize', href: '/customize' },
        { title: 'Enquiry', href: '/enquiry' },
      ],
    },
    {
      title: 'Pages Management',
      children: [
        {
          title: 'Fastpacking Gears',
          children: [{ title: 'Hero section', href: '/home/<USER>' }],
        },
      ],
    },
    {
      title: 'Blogs',
      children: [
        { title: 'Create Package', href: '/blogs/create' },
        { title: 'List Package', href: '/blogs' },
      ],
    },
    {
      title: 'Activities Offer',
      children: [
        { title: 'Create Activity', href: '/activities-offer/create' },
        { title: 'List Activities', href: '/activities-offer' },
      ],
    },
    {
      title: 'Region',
      children: [
        { title: 'Create Region', href: '/region/create' },
        { title: 'List Region', href: '/region' },
      ],
    }
  ];

  const renderMenuItem = (item: NavItem, level = 0) => {
    const hasChildren = !!item.children?.length;
    const isExpanded = expandedItems.includes(item.title);
    const isActive = item.href === pathname;

    return (
      <div key={item.title}>
        {item.href ? (
          <Link
            href={item.href}
            className={cn(
              'flex items-center px-2 py-2 hover:bg-brand-dark rounded transition-colors',
              level === 0 ? '' : 'pl-6',
              level === 0 && 'font-medium uppercase text-sm',
              isActive && 'bg-secondary'
            )}

          >
            {!collapsed && <span className="ml-2">{item.title}</span>}
          </Link>
        ) : (
          <button
            onClick={() => toggleExpand(item.title)}
            className={cn(
              'flex items-center justify-between w-full px-2 py-2 hover:bg-brand-dark rounded transition-colors',
              level > 0 && `pl-9`,
              level === 0 && 'font-medium uppercase text-sm',
              isActive && 'bg-secondary'
            )}
          >
            <div className="flex items-center">
              {!collapsed && <span className="ml-2">{item.title}</span>}
            </div>
            {!collapsed &&
              hasChildren &&
              (isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              ))}
          </button>
        )}

        {hasChildren && isExpanded && (
          <div>{item.children!.map((c) => renderMenuItem(c, level + 1))}</div>
        )}
      </div>
    );
  };

  return (
    <div
      className={`flex-none h-full bg-[#253544] text-white flex flex-col transition-all duration-200 ${collapsed ? 'w-16' : 'w-64'
        }`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-brand">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 gradient-brand rounded-lg flex items-center justify-center">
            <Mountain className="w-5 h-5 text-white" />
          </div>
          {!collapsed && (
            <div>
              <Link href="/home">
                <h2 className="font-bold text-white">Trek Admin</h2>
                <p className="text-xs text-white">Trail Management</p>
              </Link>
            </div>
          )}
        </div>
        <button
          onClick={() => setCollapsed((c) => !c)}
          className="p-2 focus:outline-none"
        >
          {collapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
        </button>
      </div>

      {/* Menu */}
      <div className="flex-1 overflow-y-auto">
        {menuItems.map((item) => renderMenuItem(item, 0))}
      </div>
    </div>
  );
}

// "use client";

// import { useState } from "react";
// import Link from "next/link";
// import {
//   BarChart3,
//   Target,
//   Mountain,
//   Compass,
//   Trophy,
//   Star,
//   Backpack,
//   Calendar,
//   Users,
//   MapPin,
//   BookOpen,
//   Camera,
//   Tent,
//   ShoppingBag,
//   Settings,
//   List,
//   ChevronLeft,
//   ChevronRight,
// } from "lucide-react";

// type NavItem = {
//   title: string;
//   url: string;
//   icon: React.FC<React.SVGProps<SVGSVGElement>>;
// };

// type TripSection = {
//   title: string;
//   icon: React.FC<React.SVGProps<SVGSVGElement>>;
//   items: NavItem[];
// };

// export default function CustomAdminSidebar() {
//   const [collapsed, setCollapsed] = useState(false);
//   const [openSections, setOpenSections] = useState<Record<string, boolean>>({});

//   const toggleSection = (title: string) =>
//     setOpenSections((prev) => ({ ...prev, [title]: !prev[title] }));

//   const tripSections: TripSection[] = [
//     {
//       title: "Trekking",
//       icon: Compass,
//       items: [
//         {
//           title: "Create Category",
//           url: "/admin/trips/trekking/category/create",
//           icon: Settings,
//         },
//         {
//           title: "List Category",
//           url: "/admin/trips/trekking/category",
//           icon: List,
//         },
//         {
//           title: "Create Package",
//           url: "/admin/trips/trekking/package/create",
//           icon: Backpack,
//         },
//         {
//           title: "List Package",
//           url: "/admin/trips/trekking/package",
//           icon: List,
//         },
//       ],
//     },
//     {
//       title: "Trail Running",
//       icon: Trophy,
//       items: [
//         {
//           title: "Create Category",
//           url: "/admin/trips/trail-running/category/create",
//           icon: Settings,
//         },
//         {
//           title: "List Category",
//           url: "/admin/trips/trail-running/category",
//           icon: List,
//         },
//         {
//           title: "Create Package",
//           url: "/admin/trips/trail-running/package/create",
//           icon: Backpack,
//         },
//         {
//           title: "List Package",
//           url: "/admin/trips/trail-running/package",
//           icon: List,
//         },
//       ],
//     },
//     {
//       title: "Peak Climbing",
//       icon: Star,
//       items: [
//         {
//           title: "Create Category",
//           url: "/admin/trips/peak-climbing/category/create",
//           icon: Settings,
//         },
//         {
//           title: "List Category",
//           url: "/admin/trips/peak-climbing/category",
//           icon: List,
//         },
//         {
//           title: "Create Package",
//           url: "/admin/trips/peak-climbing/package/create",
//           icon: Backpack,
//         },
//         {
//           title: "List Package",
//           url: "/admin/trips/peak-climbing/package",
//           icon: List,
//         },
//       ],
//     },
//     {
//       title: "Fastpacking",
//       icon: Backpack,
//       items: [
//         {
//           title: "Create Category",
//           url: "/admin/trips/fastpacking/category/create",
//           icon: Settings,
//         },
//         {
//           title: "List Category",
//           url: "/admin/trips/fastpacking/category",
//           icon: List,
//         },
//         {
//           title: "Create Package",
//           url: "/admin/trips/fastpacking/package/create",
//           icon: Backpack,
//         },
//         {
//           title: "List Package",
//           url: "/admin/trips/fastpacking/package",
//           icon: List,
//         },
//       ],
//     },
//   ];

//   const otherSections = [
//     {
//       title: "Overview",
//       items: [
//         { title: "Dashboard", url: "/", icon: BarChart3 },
//         { title: "Analytics", url: "/admin/analytics", icon: Target },
//       ],
//     },
//     {
//       title: "Operations",
//       items: [
//         { title: "Bookings", url: "/admin/bookings", icon: Calendar },
//         { title: "Customers", url: "/admin/customers", icon: Users },
//         { title: "Guides", url: "/admin/guides", icon: MapPin },
//       ],
//     },
//     {
//       title: "Content",
//       items: [
//         { title: "Blog Posts", url: "/admin/blog", icon: BookOpen },
//         { title: "Gallery", url: "/admin/gallery", icon: Camera },
//         { title: "Equipment", url: "/admin/equipment", icon: Tent },
//       ],
//     },
//     {
//       title: "System",
//       items: [
//         { title: "Gear Shop", url: "/admin/shop", icon: ShoppingBag },
//         { title: "Settings", url: "/admin/settings" },
//       ],
//     },
//   ];

//   return (
//     <div
//       className={`fixed top-0 left-0 h-full bg-brand text-white flex flex-col transition-all duration-200 ${
//         collapsed ? "w-16" : "w-64"
//       }`}
//     >
//       {/* Header */}
//       <div className="flex items-center justify-between p-4 border-b border-brand">
//         <div className="flex items-center gap-3">
//           <div className="w-8 h-8 gradient-brand rounded-lg flex items-center justify-center">
//             <Mountain className="w-5 h-5 text-white" />
//           </div>
//           {!collapsed && (
//             <div>
//               <h2 className="font-bold text-secondary">Trek Admin</h2>
//               <p className="text-xs text-secondary">Trail Management</p>
//             </div>
//           )}
//         </div>
//         <button
//           onClick={() => setCollapsed((c) => !c)}
//           className="p-2 focus:outline-none"
//         >
//           {collapsed ? <ChevronRight size={16} /> : <ChevronLeft size={16} />}
//         </button>
//       </div>

//       <div className="flex-1 overflow-y-auto">
//         {/* Trip Management */}
//         {!collapsed && (
//           <h3 className="px-2 mt-4 text-xs font-medium uppercase text-secondary/80">
//             Trip Management
//           </h3>
//         )}
//         {tripSections.map((section) => {
//           const isOpen = openSections[section.title];
//           return (
//             <div key={section.title} className="px-2 mt-2">
//               <button
//                 onClick={() => toggleSection(section.title)}
//                 className="w-full flex items-center justify-between p-2 rounded hover:bg-brand-dark transition-colors"
//               >
//                 <div className="flex items-center gap-3">
//                   <section.icon className="w-5 h-5" />
//                   {!collapsed && <span>{section.title}</span>}
//                 </div>
//                 {!collapsed && (
//                   <ChevronRight
//                     size={16}
//                     className={`transform transition-transform ${
//                       isOpen ? "rotate-90" : ""
//                     }`}
//                   />
//                 )}
//               </button>
//               {isOpen && (
//                 <ul className="mt-1 space-y-1 pl-8">
//                   {section.items.map((item) => (
//                     <li key={item.title}>
//                       <Link
//                         href={item.url}
//                         className="flex items-center gap-3 p-2 rounded hover:bg-brand-dark transition-colors"
//                       >
//                         <item.icon className="w-4 h-4" />
//                         {!collapsed && <span>{item.title}</span>}
//                       </Link>
//                     </li>
//                   ))}
//                 </ul>
//               )}
//             </div>
//           );
//         })}

//         {/* Other Sections */}
//         {otherSections.map((section) => (
//           <div key={section.title} className="mt-6">
//             {!collapsed && (
//               <h3 className="px-2 text-xs font-medium uppercase text-secondary/80">
//                 {section.title}
//               </h3>
//             )}
//             <ul className="mt-2 space-y-1 px-2">
//               {section.items.map((item) => (
//                 <li key={item.title}>
//                   <Link
//                     href={item.url}
//                     className="flex items-center gap-3 p-2 rounded hover:bg-brand-dark transition-colors"
//                   >
//                     <item.icon className="w-5 h-5" />
//                     {!collapsed && <span>{item.title}</span>}
//                   </Link>
//                 </li>
//               ))}
//             </ul>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// }

// // "use client"
// // import { useState } from "react"
// // import Link from "next/link"
// // import { usePathname } from "next/navigation"
// // import { ChevronDown, ChevronRight } from "lucide-react"
// // import { cn } from "@/lib/utils"

// // interface MenuItem {
// //   title: string
// //   href?: string
// //   children?: MenuItem[]
// // }

// // const menuItems: MenuItem[] = [
// //   {
// //     title: "Dashboard",
// //     href: "/",
// //   },
// //   {
// //     title: "Site Setting",
// //     href: "/site-setting",
// //   },
// //   {
// //     title: "Products",
// //     children: [
// //       {
// //         title: "TREKKING & HIKING",
// //         children: [
// //           { title: "Add Category", href: "/trek/create" },
// //           { title: "Categories List", href: "/trek" },
// //           { title: "Add Package", href: "/trekpackage/create" },
// //           { title: "All Packages", href: "/trekpackage" },
// //         ],
// //       },
// //       {
// //         title: "Tours",
// //         children: [
// //           { title: "Add Category", href: "/tour/create" },
// //           { title: "Categories List", href: "/tour" },
// //           { title: "Add Tour Package", href: "/tourpackage/create" },
// //           { title: "All Tour Packages", href: "/tourpackage" },
// //         ],
// //       },
// //       {
// //         title: "Adventures",
// //         children: [
// //           { title: "Add Category", href: "/adventure/create" },
// //           { title: "Category List", href: "/adventure" },
// //           { title: "Add Adventure Package", href: "/adventurepackage/create" },
// //           { title: "All Adventure Packages", href: "/adventurepackage" },
// //         ],
// //       },
// //       {
// //         title: "Heli Tours",
// //         children: [
// //           { title: "Add Heli Tour Package", href: "/helitourpackage/create" },
// //           { title: "All Heli Tour Packages", href: "/helitourpackage" },
// //         ],
// //       },
// //       {
// //         title: "Nature & Wildlife",
// //         children: [
// //           { title: "Add Nature Package", href: "/naturepackage/create" },
// //           { title: "All Nature Packages", href: "/naturepackage" },
// //         ],
// //       },
// //     ],
// //   },
// //   {
// //     title: "Booking & Enquiry",
// //     children: [
// //       { title: "Booking Info", href: "/bookings" },
// //       { title: "Customized Booking", href: "/customize" },
// //       {
// //         title: "User Enquiry",
// //         children: [
// //           { title: "Enquiry List", href: "/enquiry/lists" },
// //           { title: "Subscriber List", href: "/enquiry/subscriber" },
// //           { title: "News Letter List", href: "/enquiry/newsletter" },
// //         ],
// //       },
// //     ],
// //   },
// //   {
// //     title: "Resources",
// //     children: [
// //       { title: "Blogs", href: "/resources/blogs" },
// //       { title: "Gallery", href: "/resources/gallery" },
// //       { title: "Video", href: "/resources/video" },
// //     ],
// //   },
// //   {
// //     title: "Company",
// //     href: "/company",
// //   },
// //   {
// //     title: "Slider",
// //     children: [
// //       { title: "Add Slider", href: "/slider/add" },
// //       { title: "All Slider", href: "/slider/all" },
// //     ],
// //   },
// //   {
// //     title: "FAQS",
// //     children: [
// //       { title: "Add FAQs", href: "/faqs/add" },
// //       { title: "All FAQs", href: "/faqs/all" },
// //     ],
// //   },
// //   {
// //     title: "Pages",
// //     children: [
// //       { title: "Add Pages", href: "/pages/add" },
// //       { title: "All Pages", href: "/pages/all" },
// //     ],
// //   },
// //   {
// //     title: "Destination",
// //     href: "/destination",
// //   },
// //   {
// //     title: "Why Travel With Us?",
// //     children: [
// //       { title: "Add Why Travel With Us", href: "/why-travel/add" },
// //       { title: "All Why Travel With Us", href: "/why-travel/all" },
// //     ],
// //   },
// //   {
// //     title: "Features",
// //     children: [
// //       { title: "Add Feature", href: "/features/add" },
// //       { title: "All Feature", href: "/features/all" },
// //     ],
// //   },
// //   {
// //     title: "Activities Offer",
// //     children: [
// //       { title: "Add Activity Offer", href: "/activities/add" },
// //       { title: "All Activities Offers", href: "/activities/all" },
// //     ],
// //   },
// // ]

// // export function Sidebar() {
// //   const pathname = usePathname()
// //   const [expandedItems, setExpandedItems] = useState<string[]>(["Products", "TREKKING & HIKING"])

// //   const toggleExpanded = (title: string) => {
// //     setExpandedItems((prev) => (prev.includes(title) ? prev.filter((item) => item !== title) : [...prev, title]))
// //   }

// //   const renderMenuItem = (item: MenuItem, level = 0) => {
// //     const hasChildren = item.children && item.children.length > 0
// //     const isExpanded = expandedItems.includes(item.title)
// //     const isActive = item.href && pathname === item.href

// //     return (
// //       <div key={item.title}>
// //         {item.href ? (
// //           <Link
// //             href={item.href}
// //             className={cn(
// //               "flex items-center px-4 py-3 text-sm text-light/80 hover:text-white hover:bg-secondary transition-colors",
// //               level === 1 && "pl-8",
// //               level === 2 && "pl-12",
// //               isActive && "bg-secondary text-white",
// //             )}
// //           >
// //             {item.title}
// //           </Link>
// //         ) : (
// //           <button
// //             onClick={() => toggleExpanded(item.title)}
// //             className={cn(
// //               "flex items-center justify-between w-full px-4 py-3 text-sm text-light/80 hover:text-white hover:bg-secondary transition-colors",
// //               level === 1 && "pl-8",
// //               level === 2 && "pl-12",
// //               isExpanded && "bg-secondary text-white",
// //             )}
// //           >
// //             <div className="flex items-center">{item.title}</div>
// //             {hasChildren && (isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />)}
// //           </button>
// //         )}
// //         {hasChildren && isExpanded && (
// //           <div className="bg-brand">{item.children!.map((child) => renderMenuItem(child, level + 1))}</div>
// //         )}
// //       </div>
// //     )
// //   }

// //   return (
// //     <div className="w-64 bg-brand text-white flex flex-col">
// //       <div className="p-4 bg-brand">
// //         <h1 className="text-lg font-semibold">North Nepal(CMS)</h1>
// //         <p className="text-sm text-light/80 mt-1">North Nepal Admin</p>
// //       </div>
// //       <div className="flex-1 overflow-y-auto">
// //         <div className="p-4">
// //           <p className="text-xs text-light/80 mb-4">Menu</p>
// //           <nav className="space-y-1">{menuItems.map((item) => renderMenuItem(item))}</nav>
// //         </div>
// //       </div>
// //       <div className="p-4 bg-brand border-t border-brand">
// //         <div className="flex items-center text-sm text-light/80">
// //           <span className="mr-2">🌡️</span>
// //           <span>33°C</span>
// //         </div>
// //       </div>
// //     </div>
// //   )
// // }
