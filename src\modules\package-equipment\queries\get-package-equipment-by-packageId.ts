import { useQuery } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { IPackageEquipment } from '@/types/package_';

export function useGetPackageEquipmentById(id: string) {
  return useQuery<IApiResponse<IPackageEquipment[]>, Error>({
    queryKey: ['packages-equipment', id],
    queryFn: () =>
      fetch(`/api/package-equipment/package/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
