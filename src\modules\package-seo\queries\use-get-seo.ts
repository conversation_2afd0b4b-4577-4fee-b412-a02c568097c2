import { useQuery } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import {  IPackageSEO } from '@/types/package_';

export function useGetPackageSeoById(id: string) {
  return useQuery<IApiResponse<IPackageSEO>, Error>({
    queryKey: ['package-seo', id],
    queryFn: () =>
      fetch(`/api/package/seo/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then(res => res.json()),
    enabled: !!id,
  });
}
