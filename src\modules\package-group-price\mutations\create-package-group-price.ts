import { IPackageGroupPrice } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useCreatePackageGroupPrice() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IPackageGroupPrice>, Error, any>({
    mutationFn: (data: any) =>
      fetch(`/api/package-group-price`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['packages-group-price'] });
      toast.success('Package created successfully');
    },
    onError: () => {
      toast.error('Error creating package');
    },
  });
}
