import { IBlog } from '@/types/blogs';
import { IApiResponse } from '@/types/response';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export function useUpdateBlogs(id: string) {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IBlog>, Error, IBlog>({
    mutationFn: (data: IBlog) =>
      fetch(`/api/blogs/${id}`, {
        method: 'PATCH',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) throw new Error(res.statusText);
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['blogs'] });
      queryClient.invalidateQueries({ queryKey: ['blog', id] });
      toast.success('Blogs updated successfully');
    },
    onError: () => {
      toast.error('Error updating blog');
    },
  });
}
