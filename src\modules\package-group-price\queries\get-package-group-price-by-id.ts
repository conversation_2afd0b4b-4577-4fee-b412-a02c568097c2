import { IPackageGroupPrice } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageGroupPriceById(id: string) {
  return useQuery<IApiResponse<IPackageGroupPrice>, Error>({
    queryKey: ['packages-group-price', id],
    queryFn: () =>
      fetch(`/api/package-group-price/${id}`, {
        mode: 'cors',
        credentials: 'include',
      }).then((res) => res.json()),
    enabled: !!id,
  });
}
