import { useMutation, useQueryClient } from '@tanstack/react-query';
import { IApiResponse } from '@/types/response';
import { IOverview } from '@/types/home';
import { toast } from 'sonner';

export function UseCreateOverview() {
  const queryClient = useQueryClient();
  return useMutation<IApiResponse<IOverview>, Error, IOverview>({
    mutationFn: (data: IOverview) =>
      fetch(`/api/home-overview`, {
        method: 'POST',
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      }).then((res) => {
        if (!res.ok) {
          throw new Error(res.statusText);
        }
        return res.json();
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['overview'] });
      toast.success('Overview Created Sucessfully');
    },
    onError: () => {
      toast.error('Error Creating Overview');
    },
  });
}
