'use client';

import React, { useState, ChangeEvent, useEffect } from 'react';
import { IOverview, IOverviewPoints } from '@/types/home';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useGetHome } from '../../queries/get-home';
import { UseCreateOverview } from '../mutations/use-create-overview';
import ImageUploadSingleWithMutation from '@/modules/images/components/upload-single-image';

const CreateOverviewPage: React.FC = () => {
  const router = useRouter();
  const createOverview = UseCreateOverview();
  const { data: homeData } = useGetHome();

  const [overview, setOverview] = useState<IOverview>({
    id: '',
    heading: '',
    description: '',
    homeId: '',
    linkUrl: '',
    linkLabel: '',
    points: [],
  });

  useEffect(() => {
    const id = homeData?.data?.id;
    if (id) {
      setOverview((prev) => ({
        ...prev,
        homeId: id,
      }));
    }
  }, [homeData]);

  const handleHeadingChange = (e: ChangeEvent<HTMLInputElement>) => {
    setOverview((prev) => ({ ...prev, heading: e.target.value }));
  };

  const handleDescriptionChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setOverview((prev) => ({ ...prev, description: e.target.value }));
  };

  const handleFeatureChange = (
    index: number,
    field: keyof IOverviewPoints,
    value: string
  ) => {
    setOverview((prev) => {
      const points = [...prev.points];
      points[index] = { ...points[index], [field]: value };
      return { ...prev, points };
    });
  };

  const handleAddFeature = () => {
    setOverview((prev) => ({
      ...prev,
      points: [...prev.points, { id: '', title: '', icon: '' }],
    }));
  };

  const handleDeleteFeature = (index: number) => {
    setOverview((prev) => ({
      ...prev,
      points: prev.points.filter((_, i) => i !== index),
    }));
  };

  const handleSave = () => {
    createOverview.mutate(overview, {
      onSuccess: () => {
        router.push('/home');
      },
    });
  };

  return (
    <div className="p-6 container mx-auto space-y-6">
      <h1 className="text-3xl font-bold mb-4">Create Overview</h1>

      <div>
        <label className="block mb-1 font-medium">Heading</label>
        <input
          type="text"
          value={overview.heading}
          onChange={handleHeadingChange}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <label className="block mb-1 font-medium">Description</label>
        <textarea
          value={overview.description}
          onChange={handleDescriptionChange}
          rows={3}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <label className="block mb-1 font-medium">Link URL</label>
        <input
          type="text"
          value={overview.linkUrl}
          onChange={(e) => setOverview((prev) => ({ ...prev, linkUrl: e.target.value }))}
          className="w-full rounded border px-3 py-2"
        />
      </div>
      <div>
        <label className="block mb-1 font-medium">Link Label</label>
        <input
          type="text"
          value={overview.linkLabel}
          onChange={(e) => setOverview((prev) => ({ ...prev, linkLabel: e.target.value }))}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-3">Points</h2>
        {overview.points.map((feature, index) => (
          <div key={index} className="mb-4 border rounded p-4 bg-white">
            <input
              type="text"
              placeholder="Feature Title"
              value={feature.title}
              onChange={(e) =>
                handleFeatureChange(index, 'title', e.target.value)
              }
              className="w-full mb-2 rounded border px-3 py-2"
            />
            <ImageUploadSingleWithMutation
              label="Upload Feature Icon"
              initialUrl={feature.icon} 
              onUploaded={(url) => handleFeatureChange(index, 'icon', url)}
              accept="image/*"
            />
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteFeature(index)}
              className="mt-2"
            >
              Delete Point
            </Button>
          </div>
        ))}
        {overview.points.length < 4 && (
          <Button onClick={handleAddFeature} className="mt-3">
            + Add Points
          </Button>
        )}
      </div>

      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          className="bg-brand text-white hover:bg-brand/80"
        >
          Create Overview
        </Button>
      </div>
    </div>
  );
};

export default CreateOverviewPage;
