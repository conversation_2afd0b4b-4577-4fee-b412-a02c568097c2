import { IHomeSEO } from '@/types/home';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetHomeSEO() {
  return useQuery<IApiResponse<IHomeSEO>, Error>({
    queryKey: ['home-seo'],
    queryFn: () =>
      fetch('/api/home/<USER>', {
        mode: 'cors',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
      }).then((res) => res.json()),
  });
}
