'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ITailoredAdventure, ITailoredAdventureFeature } from '@/types/home';
import { useGetHome } from '../../queries/get-home';
import { toast } from 'sonner';
import { UseCreateTailoredAdventure } from '../mutations/use-create-tailored-adv';

const CreateTailoredAdventure: React.FC = () => {
  const router = useRouter();
  const createTailored = UseCreateTailoredAdventure();
  const { data: homeData } = useGetHome();

  const [tailored, setTailored] = useState<ITailoredAdventure>({
    id: '',
    homeId: homeData?.data?.id || '',
    title: '',
    subtitle: '',
    createdAt: '',
    updatedAt: '',
    features: [],
  });

  const handleFieldChange = (
    field: keyof ITailoredAdventure,
    value: string
  ) => {
    setTailored((prev) => ({ ...prev, [field]: value }));
  };

  const handleFeatureChange = (
    index: number,
    field: keyof ITailoredAdventureFeature,
    value: string
  ) => {
    setTailored((prev) => {
      const newFeatures = [...prev.features];
      newFeatures[index] = { ...newFeatures[index], [field]: value };
      return { ...prev, features: newFeatures };
    });
  };

  const handleAddFeature = () => {
    setTailored((prev) => ({
      ...prev,
      features: [
        ...prev.features,
        {
          id: '',
          homeTailoredAdventureId: '',
          title: '',
          subtitle: '',
          linkLabel: '',
          linkUrl: '',
          createdAt: '',
          updatedAt: '',
        },
      ],
    }));
  };

  const handleDeleteFeature = (index: number) => {
    setTailored((prev) => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  const handleSave = () => {
    if (!tailored.homeId) {
      toast.error('Home ID is required');
      return;
    }
    createTailored.mutate(tailored, {
      onSuccess: () => {
        toast.success('Tailored Adventure created successfully');
        router.push('/home');
      },
      onError: () => {
        toast.error('Error creating tailored adventure');
      },
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <h1 className="text-3xl font-bold">Create Tailored Adventure</h1>

      <div>
        <label className="block mb-1 font-medium">Title</label>
        <input
          type="text"
          value={tailored.title}
          onChange={(e) => handleFieldChange('title', e.target.value)}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <label className="block mb-1 font-medium">Subtitle</label>
        <textarea
          value={tailored.subtitle}
          onChange={(e) => handleFieldChange('subtitle', e.target.value)}
          rows={3}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-4">Features</h2>
        {tailored.features.map((feature, index) => (
          <div
            key={index}
            className="mb-4 border rounded p-4 bg-white space-y-3"
          >
            <input
              type="text"
              placeholder="Feature Title"
              value={feature.title}
              onChange={(e) =>
                handleFeatureChange(index, 'title', e.target.value)
              }
              className="w-full rounded border px-3 py-2"
            />
            <textarea
              placeholder="Feature Subtitle"
              value={feature.subtitle}
              onChange={(e) =>
                handleFeatureChange(index, 'subtitle', e.target.value)
              }
              rows={2}
              className="w-full rounded border px-3 py-2"
            />
            <input
              type="text"
              placeholder="Link Label"
              value={feature.linkLabel}
              onChange={(e) =>
                handleFeatureChange(index, 'linkLabel', e.target.value)
              }
              className="w-full rounded border px-3 py-2"
            />
            <input
              type="text"
              placeholder="Link URL"
              value={feature.linkUrl}
              onChange={(e) =>
                handleFeatureChange(index, 'linkUrl', e.target.value)
              }
              className="w-full rounded border px-3 py-2"
            />
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteFeature(index)}
              className="mt-2"
            >
              Delete Feature
            </Button>
          </div>
        ))}
        <Button onClick={handleAddFeature} className="mt-3">
          + Add Feature
        </Button>
      </div>

      <div className="flex justify-end space-x-3">
        <Button
          onClick={handleSave}
          className="bg-brand text-white hover:bg-brand/80"
        >
          Create Tailored Adventure
        </Button>
      </div>
    </div>
  );
};

export default CreateTailoredAdventure;
