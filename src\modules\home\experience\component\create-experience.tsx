'use client';

import React, { useState, ChangeEvent, useEffect } from 'react';
import { IExperience, IExperienceFeature } from '@/types/home';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { UseCreateExperience } from '../mutations/use-create-home-exp';
import { useGetHome } from '../../queries/get-home';

const CreateExperiencePage: React.FC = () => {
  const router = useRouter();
  const createExperience = UseCreateExperience();
  const { data: homeData } = useGetHome();

  const [experience, setExperience] = useState<IExperience>({
    id: '',
    heading: '',
    subHeading: '',
    homeId: '',
    features: [],
  });

  useEffect(() => {
    const id = homeData?.data?.id;
    if (id) {
      setExperience((prev) => ({
        ...prev,
        homeId: id,
      }));
    }
  }, [homeData]);

  const handleHeadingChange = (e: ChangeEvent<HTMLInputElement>) => {
    setExperience((prev) => ({ ...prev, heading: e.target.value }));
  };

  const handleSubHeadingChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setExperience((prev) => ({ ...prev, subHeading: e.target.value }));
  };

  const handleFeatureChange = (
    index: number,
    field: keyof IExperienceFeature,
    value: string
  ) => {
    setExperience((prev) => {
      const features = [...prev.features];
      features[index] = { ...features[index], [field]: value };
      return { ...prev, features };
    });
  };

  const handleAddFeature = () => {
    setExperience((prev) => ({
      ...prev,
      features: [...prev.features, { id: '', title: '', subtitle: '' }],
    }));
  };

  const handleDeleteFeature = (index: number) => {
    setExperience((prev) => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  const handleSave = () => {
    createExperience.mutate(experience, {
      onSuccess: () => {
        router.push('/home');
      },
    });
  };

  return (
    <div className="p-6 container mx-auto space-y-6">
      <h1 className="text-3xl font-bold mb-4">Create Experience</h1>

      <div>
        <label className="block mb-1 font-medium">Heading</label>
        <input
          type="text"
          value={experience.heading}
          onChange={handleHeadingChange}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <label className="block mb-1 font-medium">Subheading</label>
        <textarea
          value={experience.subHeading}
          onChange={handleSubHeadingChange}
          rows={3}
          className="w-full rounded border px-3 py-2"
        />
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-3">Features</h2>
        {experience.features.map((feature, index) => (
          <div key={index} className="mb-4 border rounded p-4 bg-white">
            <input
              type="text"
              placeholder="Feature Title"
              value={feature.title}
              onChange={(e) =>
                handleFeatureChange(index, 'title', e.target.value)
              }
              className="w-full mb-2 rounded border px-3 py-2"
            />
            <textarea
              placeholder="Feature Subtitle"
              value={feature.subtitle}
              onChange={(e) =>
                handleFeatureChange(index, 'subtitle', e.target.value)
              }
              rows={2}
              className="w-full rounded border px-3 py-2"
            />
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDeleteFeature(index)}
              className="mt-2"
            >
              Delete Feature
            </Button>
          </div>
        ))}
        {experience.features.length < 3 && (
          <Button onClick={handleAddFeature} className="mt-3">
            + Add Feature
          </Button>
        )}
      </div>

      <div className="flex justify-end">
        <Button
          onClick={handleSave}
          className="bg-brand text-white hover:bg-brand/80"
        >
          Create Experience
        </Button>
      </div>
    </div>
  );
};

export default CreateExperiencePage;
