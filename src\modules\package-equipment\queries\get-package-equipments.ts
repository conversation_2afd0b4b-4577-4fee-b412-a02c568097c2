import { IPackageEquipment } from '@/types/package_';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function useGetPackageEquipments(params?: {
  page?: number;
  limit?: number;
  sort?: string;
  searchFields?: string;
  search?: string;
}) {
  return useQuery<IApiResponse<IPackageEquipment[]>, Error>({
    queryKey: ['packages-equipment', params],
    queryFn: () =>
      fetch(
        `/api/package-equipment?${new URLSearchParams(
          params as Record<string, string>
        )}`,
        {
          mode: 'cors',
          credentials: 'include',
        }
      ).then((res) => res.json()),
  });
}
