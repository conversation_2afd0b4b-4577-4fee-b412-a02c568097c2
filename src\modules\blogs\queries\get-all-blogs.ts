import { IBlog } from '@/types/blogs';
import { IApiResponse } from '@/types/response';
import { useQuery } from '@tanstack/react-query';

export function UseGetAllBlogs() {
  return useQuery<IApiResponse<IBlog[]>, Error>({
    queryKey: ['blogs'],
    queryFn: () =>
      fetch(`/api/blogs`, {
        mode: 'cors',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }).then((res) => res.json()),
  });
}
